import {
    ObjectType,
    Field,
    ID,
    Float,
    Int,
    GraphQLISODateTime,
} from '@nestjs/graphql'
import { CertifiedData } from './certified-data.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedPensionParameters {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedData)
    certifiedData: CertifiedData

    @Field()
    certifiedDataId: string

    @Field(() => Float, { nullable: true })
    accrualPercentage?: number

    @Field(() => Float, { nullable: true })
    annualMultiplier?: number

    @Field(() => Float, { nullable: true })
    offsetAmount?: number

    @Field(() => Float, { nullable: true })
    partnersPensionPercentage?: number

    @Field(() => Int, { nullable: true })
    retirementAge?: number

    @Field(() => Float, { nullable: true })
    voluntaryContributionInterestRate?: number

    @Field(() => Float, { nullable: true })
    minimumPensionBase?: number

    @Field({ nullable: true })
    year?: string

    @Field(() => GraphQLISODateTime, { nullable: true })
    effectiveDate?: Date

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}

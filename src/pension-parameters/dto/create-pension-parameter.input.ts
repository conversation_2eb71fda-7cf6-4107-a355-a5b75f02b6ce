import {
    InputType,
    Field,
    Float,
    Int,
    ID,
    GraphQLISODateTime,
} from '@nestjs/graphql'
import {
    IsNotEmpty,
    IsString,
    IsNumber,
    IsInt,
    IsDateString,
    IsDate,
    IsOptional,
} from 'class-validator'
import { Type } from 'class-transformer'

@InputType()
export class CreatePensionParametersInput {
    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    accrualPercentage: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    annualMultiplier: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    offsetAmount: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    partnersPensionPercentage: number

    @Field(() => Int)
    @IsInt()
    @IsNotEmpty()
    retirementAge: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    voluntaryContributionInterestRate: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    minimumPensionBase: number

    @Field()
    @IsString()
    @IsNotEmpty()
    year: string

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsDate()
    @Type(() => Date)
    @IsOptional()
    effectiveDate?: Date

    @Field(() => ID)
    @IsString()
    @IsNotEmpty()
    userId: string

    @Field(() => String, { nullable: true })
    @IsString()
    @IsOptional()
    status?: string
}

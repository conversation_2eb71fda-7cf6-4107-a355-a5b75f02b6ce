import { Injectable } from '@nestjs/common'

@Injectable()
export class PensionCalculationsService {
    /**
     * Calculates the pension base from gross fulltime annual salary
     * If gross fulltime annual salary minus offset is less than minimum pension base,
     * then the pension base should be set to the minimum pension base
     *
     * @param grossFulltimeAnnualSalary - The gross fulltime annual salary
     * @param offsetAmount - The offset amount to deduct from salary
     * @param minimumPensionBase - The minimum pension base threshold
     * @returns The calculated pension base
     */
    calculatePensionBase(
        grossFulltimeAnnualSalary: number,
        offsetAmount: number,
        minimumPensionBase: number
    ): number {
        // Calculate pension base: gross salary minus offset
        const calculatedPensionBase = grossFulltimeAnnualSalary - offsetAmount

        // Apply minimum pension base rule
        return Math.max(calculatedPensionBase, minimumPensionBase)
    }

    /**
     * Calculates the fulltime annual salary from monthly salary
     *
     * @param monthlySalary - The monthly salary amount
     * @param annualMultiplier - The annual multiplier (e.g., 13 for 13th month)
     * @returns The calculated fulltime annual salary
     */
    calculateFulltimeAnnualSalary(
        monthlySalary: number,
        annualMultiplier: number
    ): number {
        return monthlySalary * annualMultiplier
    }
}

-- DropForeignKey
ALTER TABLE "CertificationRejectReason" DROP CONSTRAINT "CertificationRejectReason_personalInfoId_fkey";

-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "certifiedPensionCorrectionsEndOfYearId" UUID,
ADD COLUMN     "certifiedPensionCorrectionsStartOfYearId" UUID,
ALTER COLUMN "personalInfoId" SET DATA TYPE TEXT;

-- CreateTable
CREATE TABLE "CertifiedPensionCorrectionsStartOfYear" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "correction" DOUBLE PRECISION,
    "year" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionCorrectionsStartOfYear_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionCorrectionsEndOfYear" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "correction" DOUBLE PRECISION,
    "year" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionCorrectionsEndOfYear_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionCorrectionsStartOfYear_certifiedDataId_key" ON "CertifiedPensionCorrectionsStartOfYear"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionCorrectionsEndOfYear_certifiedDataId_key" ON "CertifiedPensionCorrectionsEndOfYear"("certifiedDataId");

-- AddForeignKey
ALTER TABLE "CertifiedPensionCorrectionsStartOfYear" ADD CONSTRAINT "CertifiedPensionCorrectionsStartOfYear_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionCorrectionsEndOfYear" ADD CONSTRAINT "CertifiedPensionCorrectionsEndOfYear_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionCorrectionsStart_fkey" FOREIGN KEY ("certifiedPensionCorrectionsStartOfYearId") REFERENCES "CertifiedPensionCorrectionsStartOfYear"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionCorrectionsEndOf_fkey" FOREIGN KEY ("certifiedPensionCorrectionsEndOfYearId") REFERENCES "CertifiedPensionCorrectionsEndOfYear"("id") ON DELETE SET NULL ON UPDATE CASCADE;

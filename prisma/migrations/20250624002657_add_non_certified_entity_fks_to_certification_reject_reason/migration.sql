-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "addressId" UUID,
ADD COLUMN     "childId" UUID,
ADD COLUMN     "employmentInfoId" UUID,
ADD COLUMN     "partnerInfoId" UUID,
ADD COLUMN     "pensionInfoId" UUID,
ADD COLUMN     "salaryEntryId" UUID;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_partnerInfoId_fkey" FOREIGN KEY ("partnerInfoId") REFERENCES "PartnerInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_childId_fkey" FOREIGN KEY ("childId") REFERENCES "Child"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_employmentInfoId_fkey" FOREIGN KEY ("employmentInfoId") REFERENCES "EmploymentInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_pensionInfoId_fkey" FOREIGN KEY ("pensionInfoId") REFERENCES "PensionInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_salaryEntryId_fkey" FOREIGN KEY ("salaryEntryId") REFERENCES "SalaryEntry"("id") ON DELETE SET NULL ON UPDATE CASCADE;

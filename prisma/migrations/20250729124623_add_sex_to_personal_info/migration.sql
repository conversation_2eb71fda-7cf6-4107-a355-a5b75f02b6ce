/*
  Warnings:

  - Added the required column `sex` to the `PersonalInfo` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "CertifiedPersonalInfo" ADD COLUMN     "sex" TEXT;

-- AlterTable - Add column with default value first
ALTER TABLE "PersonalInfo" ADD COLUMN     "sex" TEXT NOT NULL DEFAULT 'Male';

-- Remove default constraint after adding the column
ALTER TABLE "PersonalInfo" ALTER COLUMN "sex" DROP DEFAULT;

/*
  Warnings:

  - The `approvalStatus` column on the `Participant` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "personalInfoId" UUID;

-- AlterTable
ALTER TABLE "Participant" DROP COLUMN "approvalStatus",
ADD COLUMN     "approvalStatus" TEXT NOT NULL DEFAULT 'PENDING';

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;
